#!/bin/bash

# Shred Collector Script
# Collects real shred data from Jito Shredstream for testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
TARGET_COUNT=${TARGET_SHRED_COUNT:-100}
OUTPUT_DIR=${OUTPUT_DIR:-tests/data}

echo -e "${GREEN}🚀 Starting Shred Collector${NC}"
echo -e "${YELLOW}Target shreds: ${TARGET_COUNT}${NC}"
echo -e "${YELLOW}Output directory: ${OUTPUT_DIR}${NC}"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found. Please copy .env.example to .env and configure it.${NC}"
    exit 1
fi

# Create output directory if it doesn't exist
mkdir -p "${OUTPUT_DIR}"

# Check if cargo is available
if ! command -v cargo &> /dev/null; then
    echo -e "${RED}❌ Cargo not found. Please install Rust and Cargo.${NC}"
    exit 1
fi

echo -e "${GREEN}📡 Connecting to Jito Shredstream...${NC}"

# Run the shred collector
cargo run --bin shred_collector --features "tokio,solana-stream-sdk,dotenvy"

echo -e "${GREEN}✅ Shred collection completed!${NC}"
echo -e "${YELLOW}📁 Check ${OUTPUT_DIR}/ for collected shred files${NC}"
