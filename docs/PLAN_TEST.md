# Integration Test Plan for NAPI Shredstream Decoder

## Project Context

### Overview

This is a Node.js addon using NAPI to create Rust helper functions for decoding Jito shredstream data and exposing them to TypeScript. The project wraps Solana blockchain data structures for TypeScript interoperability.

### Current Architecture

```
src/
├── lib.rs                    # Main NAPI entry point with decode_entries()
├── types/                    # TypeScript-compatible wrapper structs
│   ├── entries.rs           # Entry, ParsedEntry
│   ├── hashes.rs            # Hash
│   ├── pubkeys.rs           # Pubkey
│   ├── signatures.rs        # Signature
│   ├── messages.rs          # MessageHeader, CompiledInstruction, etc.
│   └── transactions.rs      # VersionedTransaction, VersionedMessage
└── utils/
    └── conversions.rs       # convert_entries_to_parsed()
```

### Data Flow

1. Raw shred bytes → `decode_entries(bytes)` → `ParsedEntry` for TypeScript
2. Uses `bincode::deserialize()` to get `Vec<solana_entry::entry::Entry>`
3. Converts via `convert_entries_to_parsed()` using `from_solana_*` methods

### Wrapper Structs Inventory

**Core Types:**

-   `Hash` (wraps `solana_hash::Hash`)
-   `Pubkey` (wraps `solana_pubkey::Pubkey`)
-   `Signature` (wraps `solana_signature::Signature`)

**Message Types:**

-   `MessageHeader` (wraps `solana_message::MessageHeader`)
-   `CompiledInstruction` (wraps `solana_message::compiled_instruction::CompiledInstruction`)
-   `MessageAddressTableLookup` (wraps `solana_message::v0::MessageAddressTableLookup`)
-   `LegacyMessage` (wraps `solana_message::Message`)
-   `V0Message` (wraps `solana_message::v0::Message`)

**Transaction Types:**

-   `VersionedMessage` (wraps `solana_message::VersionedMessage`)
-   `VersionedTransaction` (wraps `solana_transaction::versioned::VersionedTransaction`)

**Entry Types:**

-   `Entry` (wraps `solana_entry::entry::Entry`)
-   `ParsedEntry` (container for `Vec<Entry>`)

### Test Data Available

-   Real shred data in `tests/data/shred_*.bin` (222 files collected from Jito Shredstream)
-   File sizes: 3KB - 54KB
-   Slots: 344333101 - 344333130
-   Commitment level: Confirmed

## Test Strategy

### Methodology: Round-Trip Validation

For each wrapper struct, implement comprehensive round-trip testing:

1. **Original → Wrapped**: Use existing `from_solana_*` methods
2. **Wrapped → Original**: Implement new `to_solana_*` methods
3. **Field-by-Field Comparison**: Verify 100% data integrity
4. **Edge Case Testing**: Handle malformed/empty data

### Test Categories

#### Category 1: Primitive Wrapper Tests

**Target Structs:** `Hash`, `Pubkey`, `Signature`
**Test Focus:** Byte array conversion accuracy
**Implementation:** Direct byte comparison

#### Category 2: Message Component Tests

**Target Structs:** `MessageHeader`, `CompiledInstruction`, `MessageAddressTableLookup`
**Test Focus:** Field mapping and type conversion
**Implementation:** Field-by-field validation

#### Category 3: Complex Message Tests

**Target Structs:** `LegacyMessage`, `V0Message`, `VersionedMessage`
**Test Focus:** Nested struct conversion and variant handling
**Implementation:** Recursive validation

#### Category 4: Transaction Tests

**Target Structs:** `VersionedTransaction`
**Test Focus:** Complete transaction structure integrity
**Implementation:** Full transaction round-trip

#### Category 5: Entry Integration Tests

**Target Structs:** `Entry`, `ParsedEntry`
**Test Focus:** End-to-end shred decoding with real data
**Implementation:** Real shred file processing

## Implementation Plan

### Phase 1: Foundation Setup

**Files to Create:**

-   `tests/integration_test.rs` - Main test file
-   `src/utils/reverse_conversions.rs` - Reverse conversion functions
-   `tests/helpers/mod.rs` - Test helper functions

**Key Functions to Implement:**

```rust
// In src/utils/reverse_conversions.rs
pub fn hash_to_solana(hash: &Hash) -> solana_hash::Hash
pub fn pubkey_to_solana(pubkey: &Pubkey) -> solana_pubkey::Pubkey
pub fn signature_to_solana(signature: &Signature) -> solana_signature::Signature
pub fn entry_to_solana(entry: &Entry) -> solana_entry::entry::Entry
// ... and all other reverse conversions
```

### Phase 2: Test Infrastructure

**Test Helper Functions:**

```rust
// In tests/helpers/mod.rs
pub fn load_test_shred(filename: &str) -> Vec<u8>
pub fn deserialize_shred(bytes: &[u8]) -> Vec<solana_entry::entry::Entry>
pub fn compare_entries(original: &solana_entry::entry::Entry, converted: &solana_entry::entry::Entry) -> bool
pub fn run_round_trip_test<T, U>(original: &T, convert_fn: fn(&T) -> U, reverse_fn: fn(&U) -> T) -> bool
```

### Phase 3: Progressive Testing

**Test Implementation Order:**

1. Primitive types (Hash, Pubkey, Signature)
2. Simple message components (MessageHeader, CompiledInstruction)
3. Complex messages (LegacyMessage, V0Message)
4. Transactions (VersionedTransaction)
5. Entries (Entry, ParsedEntry)
6. Real data integration tests

## Progress Tracking

### Completion Checklist

#### ✅ Infrastructure Setup

-   [ ] Create `tests/integration_test.rs`
-   [ ] Create `src/utils/reverse_conversions.rs`
-   [ ] Create `tests/helpers/mod.rs`
-   [ ] Add test dependencies to `Cargo.toml`

#### ✅ Reverse Conversion Functions

-   [ ] `hash_to_solana()`
-   [ ] `pubkey_to_solana()`
-   [ ] `signature_to_solana()`
-   [ ] `message_header_to_solana()`
-   [ ] `compiled_instruction_to_solana()`
-   [ ] `message_address_table_lookup_to_solana()`
-   [ ] `legacy_message_to_solana()`
-   [ ] `v0_message_to_solana()`
-   [ ] `versioned_message_to_solana()`
-   [ ] `versioned_transaction_to_solana()`
-   [ ] `entry_to_solana()`

#### ✅ Unit Tests (Round-Trip)

-   [ ] Hash round-trip test
-   [ ] Pubkey round-trip test
-   [ ] Signature round-trip test
-   [ ] MessageHeader round-trip test
-   [ ] CompiledInstruction round-trip test
-   [ ] MessageAddressTableLookup round-trip test
-   [ ] LegacyMessage round-trip test
-   [ ] V0Message round-trip test
-   [ ] VersionedMessage round-trip test
-   [ ] VersionedTransaction round-trip test
-   [ ] Entry round-trip test

#### ✅ Integration Tests (Real Data)

-   [ ] Load and parse real shred files
-   [ ] Test decode_entries() with real data
-   [ ] Validate ParsedEntry structure
-   [ ] Test multiple shred files
-   [ ] Performance benchmarks
-   [ ] Error handling tests

#### ✅ Coverage Analysis

-   [ ] Achieve 100% line coverage
-   [ ] Achieve 100% branch coverage
-   [ ] Document any uncoverable code
-   [ ] Generate coverage report

### Success Criteria

1. **100% Round-Trip Accuracy**: All data survives Original → Wrapped → Original conversion
2. **Real Data Compatibility**: Successfully processes all 222 test shred files
3. **Complete Coverage**: Tests cover all wrapper structs and conversion functions
4. **Performance Validation**: Conversion overhead is minimal
5. **Error Resilience**: Graceful handling of malformed data

## Technical Specifications

### Test Dependencies

```toml
[dev-dependencies]
solana-entry = "2.2.7"
solana-transaction = "2.2.2"
solana-message = "2.2.1"
solana-pubkey = "2.2.1"
solana-hash = "2.2.1"
solana-signature = "2.2.1"
```

### Test Data Management

-   Use `tests/data/shred_*.bin` files
-   Implement lazy loading for large datasets
-   Cache parsed results for performance
-   Test with subset for CI, full set for comprehensive validation

### Error Scenarios to Test

-   Empty byte arrays
-   Invalid signatures
-   Malformed messages
-   Oversized data
-   Network byte order issues
-   Unicode handling in strings

## AI Assistant Guidelines

### Context Preservation

This document serves as the single source of truth for the integration testing effort. Any AI assistant working on this project should:

1. **Read this document first** to understand current progress
2. **Update progress checkboxes** as work is completed
3. **Add new findings** to relevant sections
4. **Maintain consistency** with established patterns
5. **Document any deviations** from the plan

### Handoff Protocol

When stopping work, update:

-   [ ] Progress checkboxes with current status
-   [ ] Any new issues discovered
-   [ ] Next recommended steps
-   [ ] Code quality observations

### Code Quality Standards

-   Follow existing project patterns
-   Use descriptive test names
-   Include comprehensive error messages
-   Add inline documentation for complex logic
-   Maintain consistent formatting

---

**Last Updated:** Initial creation
**Next Phase:** Infrastructure Setup
**Estimated Completion:** TBD based on implementation progress
