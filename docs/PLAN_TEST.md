# Kế Hoạch Test Toàn Diện cho napi-shredstream-decoder

## Tổng Quan

Xây dựng hệ thống test toàn diện để đảm bảo tính chính xác 100% của wrapper structs và conversion functions trong dự án napi-shredstream-decoder. <PERSON><PERSON><PERSON> tiêu là verify data integrity hoàn toàn giữa <PERSON> structs gốc và wrapper structs đã tạo.

## Bước 1: Thu Thập Dữ Liệu Thật từ Jito

### 1.1 Phân Tích Yêu Cầu
- **Mục tiêu**: Thu thập dữ liệu Entry thật từ Jito shredstream để làm test data
- **Định dạng**: Binary data có thể deserialize thành `Vec<solana_entry::entry::Entry>`
- **Lưu trữ**: File binary trong thư mục `tests/data/`

### 1.2 Implementation Plan

#### 1.2.1 Tạo Data Collector Module
```
src/collector/
├── mod.rs              # Module exports
├── jito_client.rs      # Jito connection và data fetching
├── data_saver.rs       # Save collected data to files
└── config.rs           # Configuration management
```

#### 1.2.2 Dependencies Cần Thêm
```toml
[dev-dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
serde_json = "1.0"
env_logger = "0.10"
```

#### 1.2.3 Configuration
- Tạo file `.env.test` với Jito endpoint configuration
- Environment variables cho test data collection
- Timeout và retry settings

### 1.3 Data Collection Strategy
1. **Real-time Collection**: Connect tới Jito shredstream
2. **Sample Diversity**: Thu thập các loại entries khác nhau:
   - Tick entries (empty transactions)
   - Single transaction entries
   - Multiple transaction entries
   - Different message types (Legacy vs V0)
3. **Data Validation**: Verify collected data có thể deserialize thành công
4. **Storage Format**: Binary files với naming convention `test_data_YYYYMMDD_HHMMSS.bin`

## Bước 2: Comprehensive Test Suite

### 2.1 Test Structure
```
tests/
├── integration_test.rs     # Main integration tests
├── data/                   # Test data files
│   ├── sample_entries_1.bin
│   ├── sample_entries_2.bin
│   └── edge_cases.bin
├── helpers/                # Test helper functions
│   ├── mod.rs
│   ├── comparisons.rs      # Deep comparison functions
│   ├── generators.rs       # Test data generators
│   └── assertions.rs       # Custom assertion macros
└── reverse_conversion/     # Reverse conversion tests
    ├── mod.rs
    └── entry_converter.rs
```

### 2.2 Test Categories

#### 2.2.1 Deserialization Tests
```rust
#[test]
fn test_original_solana_deserialization() {
    // Test deserialize dữ liệu thật bằng bincode::deserialize::<Vec<Entry>>()
    // Verify deserialization thành công với struct Entry gốc
}

#[test]
fn test_wrapper_deserialization() {
    // Test decode_entries() function với cùng dữ liệu
    // Verify trả về ParsedEntry với cấu trúc đúng
}
```

#### 2.2.2 Data Integrity Tests
```rust
#[test]
fn test_field_by_field_integrity() {
    // So sánh chi tiết từng field giữa Entry gốc và wrapper
    // Verify: num_hashes, hash, transactions
}

#[test]
fn test_transaction_integrity() {
    // Deep comparison của transaction data
    // Verify: signatures, message, instructions
}

#[test]
fn test_message_integrity() {
    // Verify message fields: header, account_keys, recent_blockhash, instructions
}

#[test]
fn test_instruction_integrity() {
    // Verify instruction fields: program_id, accounts, data
}
```

#### 2.2.3 Reverse Conversion Tests
```rust
#[test]
fn test_reverse_conversion_roundtrip() {
    // Original -> Wrapper -> Original
    // Verify data không bị loss hoặc corruption
}

#[test]
fn test_bidirectional_conversion() {
    // Test conversion cả 2 chiều
    // Ensure perfect data preservation
}
```

#### 2.2.4 Edge Cases Tests
```rust
#[test]
fn test_empty_transactions() {
    // Test tick entries (no transactions)
}

#[test]
fn test_multiple_transactions() {
    // Test entries với nhiều transactions
}

#[test]
fn test_different_message_types() {
    // Test Legacy vs V0 messages
}

#[test]
fn test_large_datasets() {
    // Performance và accuracy với large data
}
```

### 2.3 Helper Functions

#### 2.3.1 Deep Comparison Functions
```rust
// tests/helpers/comparisons.rs
pub fn compare_entries(original: &solana_entry::entry::Entry, wrapper: &Entry) -> bool
pub fn compare_transactions(original: &VersionedTransaction, wrapper: &VersionedTransaction) -> bool
pub fn compare_messages(original: &Message, wrapper: &Message) -> bool
pub fn compare_instructions(original: &Instruction, wrapper: &Instruction) -> bool
```

#### 2.3.2 Custom Assertions
```rust
// tests/helpers/assertions.rs
macro_rules! assert_entry_eq { ... }
macro_rules! assert_transaction_eq { ... }
macro_rules! assert_message_eq { ... }
```

## Bước 3: Reverse Conversion Implementation

### 3.1 Reverse Conversion Functions
```rust
// src/utils/reverse_conversions.rs
impl Entry {
    pub fn to_solana_entry(&self) -> solana_entry::entry::Entry { ... }
}

impl VersionedTransaction {
    pub fn to_solana_versioned_transaction(&self) -> solana_transaction::VersionedTransaction { ... }
}

// Tương tự cho tất cả wrapper types
```

### 3.2 Validation Strategy
- Convert từ wrapper về original
- So sánh byte-by-byte nếu có thể
- Field-by-field comparison cho complex types
- Verify serialization/deserialization consistency

## Bước 4: Test Execution và Validation

### 4.1 Test Commands
```bash
# Run all tests
cargo test

# Run specific test categories
cargo test integration_test
cargo test reverse_conversion
cargo test edge_cases

# Run with detailed output
cargo test -- --nocapture

# Run performance tests
cargo test --release performance
```

### 4.2 Success Criteria
- **100% Pass Rate**: Tất cả tests phải pass
- **Zero Data Loss**: Không có field nào bị mất trong conversion
- **Perfect Accuracy**: Byte-perfect hoặc field-perfect comparison
- **Performance**: Conversion không làm chậm đáng kể
- **Coverage**: Test coverage >= 95% cho conversion code

### 4.3 Continuous Integration
- Integrate tests vào CI/CD pipeline
- Automated test data collection (nếu có thể)
- Performance regression detection
- Test result reporting

## Bước 5: Documentation và Maintenance

### 5.1 Test Documentation
- README cho test suite
- Test data format documentation
- Troubleshooting guide
- Performance benchmarks

### 5.2 Maintenance Plan
- Regular test data updates
- Performance monitoring
- Test suite expansion khi có new features
- Compatibility testing với Solana updates

## Timeline Ước Tính

- **Bước 1** (Data Collection): 2-3 ngày
- **Bước 2** (Test Suite): 3-4 ngày  
- **Bước 3** (Reverse Conversion): 2-3 ngày
- **Bước 4** (Validation): 1-2 ngày
- **Bước 5** (Documentation): 1 ngày

**Tổng cộng**: 9-13 ngày

## Deliverables

1. ✅ **Data Collector Module**: Thu thập real data từ Jito
2. ✅ **Test Data Files**: Binary files với diverse test cases
3. ✅ **Comprehensive Test Suite**: Full coverage integration tests
4. ✅ **Reverse Conversion Functions**: Bidirectional conversion
5. ✅ **Validation Logic**: Deep comparison và integrity checks
6. ✅ **Documentation**: Complete test documentation
7. ✅ **CI Integration**: Automated testing pipeline

## Success Metrics

- **Data Integrity**: 100% field accuracy
- **Test Coverage**: >= 95% code coverage
- **Performance**: < 10% overhead cho conversion
- **Reliability**: Zero false positives/negatives
- **Maintainability**: Easy to extend và update
