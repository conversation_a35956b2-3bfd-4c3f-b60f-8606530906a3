[package]
edition = "2021"
name = "shredstream-decoder"
version = "0.0.0"

[lib]
crate-type = ["cdylib"]

[dependencies]
napi = { version = "2.12.2", default-features = false, features = ["napi8"] }
napi-derive = "2.12.2"
serde = { version = "1.0.219", features = ["derive"] }
solana-entry = "2.2.7"
bincode = "1.3.3"
solana-hash = "2.2.1"
solana-signature = "2.2.1"
solana-pubkey = "2.2.1"
solana-message = "2.2.1"
solana-transaction = "2.2.1"
tokio = { version = "1.0", features = [
    "rt-multi-thread",
    "macros",
], optional = true }
solana-stream-sdk = { version = "0.2.5", optional = true }
dotenvy = { version = "0.15", optional = true }

[build-dependencies]
napi-build = "2.0.1"

[profile.release]
lto = true
strip = "symbols"

[[bin]]
name = "shred_collector"
path = "bin/shred_collector.rs"
required-features = ["tokio", "solana-stream-sdk", "dotenvy"]
